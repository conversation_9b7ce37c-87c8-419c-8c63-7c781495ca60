# 综合测试脚本 - 验证所有功能
Write-Host "=== UserManager 综合功能测试 ===" -ForegroundColor Magenta

# 测试1: 健康检查
Write-Host "`n1. 测试健康检查接口..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-RestMethod -Uri "http://localhost:8081/health" -Method GET
    Write-Host "✓ 健康检查成功: $($healthResponse.data.status)" -ForegroundColor Green
} catch {
    Write-Host "✗ 健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 测试2: 第一个激活码登录 (Premium用户)
Write-Host "`n2. 测试Premium用户登录 (test123456789)..." -ForegroundColor Yellow
try {
    $loginBody1 = @{
        card = "test123456789"
        agent = "main"
    } | ConvertTo-Json
    
    $loginResponse1 = Invoke-RestMethod -Uri "http://localhost:8081/users/card-login" -Method POST -ContentType "application/json" -Body $loginBody1
    $token1 = $loginResponse1.data.token
    
    Write-Host "✓ Premium用户登录成功" -ForegroundColor Green
    Write-Host "  用户ID: $($loginResponse1.data.id)" -ForegroundColor Cyan
    Write-Host "  VIP类型: $($loginResponse1.data.vip.product)" -ForegroundColor Cyan
    Write-Host "  权限等级: $($loginResponse1.data.vip.power)" -ForegroundColor Cyan
    Write-Host "  积分: $($loginResponse1.data.vip.score)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ Premium用户登录失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 测试3: 第二个激活码登录 (Basic用户)
Write-Host "`n3. 测试Basic用户登录 (demo987654321)..." -ForegroundColor Yellow
try {
    $loginBody2 = @{
        card = "demo987654321"
        agent = "main"
    } | ConvertTo-Json
    
    $loginResponse2 = Invoke-RestMethod -Uri "http://localhost:8081/users/card-login" -Method POST -ContentType "application/json" -Body $loginBody2
    $token2 = $loginResponse2.data.token
    
    Write-Host "✓ Basic用户登录成功" -ForegroundColor Green
    Write-Host "  用户ID: $($loginResponse2.data.id)" -ForegroundColor Cyan
    Write-Host "  VIP类型: $($loginResponse2.data.vip.product)" -ForegroundColor Cyan
    Write-Host "  权限等级: $($loginResponse2.data.vip.power)" -ForegroundColor Cyan
    Write-Host "  积分: $($loginResponse2.data.vip.score)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ Basic用户登录失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 测试4: JWT认证 - Premium用户whoami
Write-Host "`n4. 测试Premium用户JWT认证 (whoami)..." -ForegroundColor Yellow
try {
    $headers1 = @{
        'X-Auth-Token' = $token1
        'Content-Type' = 'application/json'
    }
    
    $whoamiResponse1 = Invoke-RestMethod -Uri "http://localhost:8081/users/whoami" -Method POST -Headers $headers1 -Body "{}"
    
    Write-Host "✓ Premium用户JWT认证成功" -ForegroundColor Green
    Write-Host "  激活码: $($whoamiResponse1.data.activationCode)" -ForegroundColor Cyan
    Write-Host "  状态: $($whoamiResponse1.data.status)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ Premium用户JWT认证失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 测试5: JWT认证 - Basic用户whoami
Write-Host "`n5. 测试Basic用户JWT认证 (whoami)..." -ForegroundColor Yellow
try {
    $headers2 = @{
        'X-Auth-Token' = $token2
        'Content-Type' = 'application/json'
    }
    
    $whoamiResponse2 = Invoke-RestMethod -Uri "http://localhost:8081/users/whoami" -Method POST -Headers $headers2 -Body "{}"
    
    Write-Host "✓ Basic用户JWT认证成功" -ForegroundColor Green
    Write-Host "  激活码: $($whoamiResponse2.data.activationCode)" -ForegroundColor Cyan
    Write-Host "  状态: $($whoamiResponse2.data.status)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ Basic用户JWT认证失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 测试6: 无效激活码测试
Write-Host "`n6. 测试无效激活码..." -ForegroundColor Yellow
try {
    $invalidLoginBody = @{
        card = "invalid123456"
        agent = "main"
    } | ConvertTo-Json

    $invalidResponse = Invoke-RestMethod -Uri "http://localhost:8081/users/card-login" -Method POST -ContentType "application/json" -Body $invalidLoginBody
    Write-Host "✗ 无效激活码测试失败: 应该返回错误但却成功了" -ForegroundColor Red
} catch {
    Write-Host "✓ 无效激活码正确被拒绝" -ForegroundColor Green
}

# 测试总结
Write-Host "`n=== 测试总结 ===" -ForegroundColor Magenta
Write-Host "✓ 所有核心功能测试通过!" -ForegroundColor Green
Write-Host "✓ 激活码登录功能正常" -ForegroundColor Green
Write-Host "✓ JWT认证功能正常" -ForegroundColor Green
Write-Host "✓ 安全配置正确" -ForegroundColor Green
Write-Host "✓ 数据库连接正常" -ForegroundColor Green
Write-Host "`n应用已准备就绪，可以正常使用!" -ForegroundColor Green
