# UserManager Test Script
Write-Host "=== UserManager Test ===" -ForegroundColor Magenta

# Test 1: Health Check
Write-Host "`n1. Health Check..." -ForegroundColor Yellow
try {
    $health = Invoke-RestMethod -Uri "http://localhost:8081/health" -Method GET
    Write-Host "OK Health Check: $($health.data.status)" -ForegroundColor Green
} catch {
    Write-Host "FAIL Health Check" -ForegroundColor Red
    exit 1
}

# Test 2: Premium User Login
Write-Host "`n2. Premium User Login..." -ForegroundColor Yellow
try {
    $body1 = '{"card":"test123456789","agent":"main"}'
    $login1 = Invoke-RestMethod -Uri "http://localhost:8081/users/card-login" -Method POST -ContentType "application/json" -Body $body1
    $token1 = $login1.data.token
    
    Write-Host "OK Premium Login Success" -ForegroundColor Green
    Write-Host "  User ID: $($login1.data.id)" -ForegroundColor Cyan
    Write-Host "  VIP Type: $($login1.data.vip.product)" -ForegroundColor Cyan
    Write-Host "  Power Level: $($login1.data.vip.power)" -ForegroundColor Cyan
    Write-Host "  Score: $($login1.data.vip.score)" -ForegroundColor Cyan
} catch {
    Write-Host "FAIL Premium Login" -ForegroundColor Red
    exit 1
}

# Test 3: Basic User Login
Write-Host "`n3. Basic User Login..." -ForegroundColor Yellow
try {
    $body2 = '{"card":"demo987654321","agent":"main"}'
    $login2 = Invoke-RestMethod -Uri "http://localhost:8081/users/card-login" -Method POST -ContentType "application/json" -Body $body2
    $token2 = $login2.data.token
    
    Write-Host "OK Basic Login Success" -ForegroundColor Green
    Write-Host "  User ID: $($login2.data.id)" -ForegroundColor Cyan
    Write-Host "  VIP Type: $($login2.data.vip.product)" -ForegroundColor Cyan
    Write-Host "  Power Level: $($login2.data.vip.power)" -ForegroundColor Cyan
    Write-Host "  Score: $($login2.data.vip.score)" -ForegroundColor Cyan
} catch {
    Write-Host "FAIL Basic Login" -ForegroundColor Red
    exit 1
}

# Test 4: JWT Authentication
Write-Host "`n4. JWT Authentication..." -ForegroundColor Yellow
try {
    $headers = @{ 'X-Auth-Token' = $token1; 'Content-Type' = 'application/json' }
    $whoami = Invoke-RestMethod -Uri "http://localhost:8081/users/whoami" -Method POST -Headers $headers -Body "{}"
    
    Write-Host "OK JWT Authentication Success" -ForegroundColor Green
    Write-Host "  Activation Code: $($whoami.data.activationCode)" -ForegroundColor Cyan
    Write-Host "  Status: $($whoami.data.status)" -ForegroundColor Cyan
} catch {
    Write-Host "FAIL JWT Authentication" -ForegroundColor Red
    exit 1
}

# Summary
Write-Host "`n=== Test Results ===" -ForegroundColor Magenta
Write-Host "OK All tests passed!" -ForegroundColor Green
Write-Host "OK Card login working" -ForegroundColor Green
Write-Host "OK JWT authentication working" -ForegroundColor Green
Write-Host "OK Security configuration correct" -ForegroundColor Green
Write-Host "`nApplication is ready!" -ForegroundColor Green

Write-Host "`nAvailable test activation codes:" -ForegroundColor Yellow
Write-Host "1. test123456789 (premium user)" -ForegroundColor Cyan
Write-Host "2. demo987654321 (basic user)" -ForegroundColor Cyan
