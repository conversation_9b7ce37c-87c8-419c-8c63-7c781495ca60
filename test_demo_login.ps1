# 测试第二个激活码登录
Write-Host "测试第二个激活码登录..." -ForegroundColor Yellow

try {
    $loginBody = @{
        card = "demo987654321"
        agent = "main"
    } | ConvertTo-Json
    
    Write-Host "请求体: $loginBody" -ForegroundColor Cyan
    
    $response = Invoke-RestMethod -Uri "http://localhost:8081/users/card-login" -Method POST -ContentType "application/json" -Body $loginBody
    
    Write-Host "登录成功!" -ForegroundColor Green
    Write-Host "用户ID: $($response.data.id)" -ForegroundColor Green
    Write-Host "Token: $($response.data.token.Substring(0, 20))..." -ForegroundColor Green
    Write-Host "VIP类型: $($response.data.vip.product)" -ForegroundColor Green
    Write-Host "VIP权限等级: $($response.data.vip.power)" -ForegroundColor Green
    Write-Host "VIP积分: $($response.data.vip.score)" -ForegroundColor Green
    
} catch {
    Write-Host "登录失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "状态码: $statusCode" -ForegroundColor Red
    }
}
