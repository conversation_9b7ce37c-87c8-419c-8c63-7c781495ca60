# 测试完整流程：登录 -> 获取用户信息
Write-Host "测试完整流程..." -ForegroundColor Yellow

try {
    # 1. 先登录获取token
    Write-Host "1. 登录获取token..." -ForegroundColor Cyan
    $loginBody = @{
        card = "test123456789"
        agent = "main"
    } | ConvertTo-Json
    
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:8081/users/card-login" -Method POST -ContentType "application/json" -Body $loginBody
    $token = $loginResponse.data.token
    Write-Host "登录成功，Token: $($token.Substring(0, 20))..." -ForegroundColor Green
    
    # 2. 使用token获取用户信息
    Write-Host "2. 获取用户信息..." -ForegroundColor Cyan
    $headers = @{
        'X-Auth-Token' = $token
        'Content-Type' = 'application/json'
    }
    
    $whoamiResponse = Invoke-RestMethod -Uri "http://localhost:8081/users/whoami" -Method POST -Headers $headers -Body "{}"
    
    Write-Host "用户信息获取成功!" -ForegroundColor Green
    Write-Host "用户ID: $($whoamiResponse.data.id)" -ForegroundColor Green
    Write-Host "激活码: $($whoamiResponse.data.activationCode)" -ForegroundColor Green
    Write-Host "代理: $($whoamiResponse.data.agent)" -ForegroundColor Green
    Write-Host "状态: $($whoamiResponse.data.status)" -ForegroundColor Green
    Write-Host "VIP类型: $($whoamiResponse.data.vip.product)" -ForegroundColor Green
    Write-Host "VIP权限: $($whoamiResponse.data.vip.power)" -ForegroundColor Green
    Write-Host "VIP积分: $($whoamiResponse.data.vip.score)" -ForegroundColor Green
    
} catch {
    Write-Host "测试失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "状态码: $statusCode" -ForegroundColor Red
    }
}
