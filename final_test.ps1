# 最终测试脚本
Write-Host "=== UserManager 功能验证 ===" -ForegroundColor Magenta

# 测试1: 健康检查
Write-Host "`n1. 健康检查..." -ForegroundColor Yellow
try {
    $health = Invoke-RestMethod -Uri "http://localhost:8081/health" -Method GET
    Write-Host "✓ 健康检查成功: $($health.data.status)" -ForegroundColor Green
} catch {
    Write-Host "✗ 健康检查失败" -ForegroundColor Red
    exit 1
}

# 测试2: Premium用户登录
Write-Host "`n2. Premium用户登录..." -ForegroundColor Yellow
try {
    $body1 = '{"card":"test123456789","agent":"main"}'
    $login1 = Invoke-RestMethod -Uri "http://localhost:8081/users/card-login" -Method POST -ContentType "application/json" -Body $body1
    $token1 = $login1.data.token
    
    Write-Host "✓ Premium用户登录成功" -ForegroundColor Green
    Write-Host "  用户ID: $($login1.data.id)" -ForegroundColor Cyan
    Write-Host "  VIP类型: $($login1.data.vip.product)" -ForegroundColor Cyan
    Write-Host "  权限等级: $($login1.data.vip.power)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ Premium用户登录失败" -ForegroundColor Red
    exit 1
}

# 测试3: Basic用户登录
Write-Host "`n3. Basic用户登录..." -ForegroundColor Yellow
try {
    $body2 = '{"card":"demo987654321","agent":"main"}'
    $login2 = Invoke-RestMethod -Uri "http://localhost:8081/users/card-login" -Method POST -ContentType "application/json" -Body $body2
    $token2 = $login2.data.token
    
    Write-Host "✓ Basic用户登录成功" -ForegroundColor Green
    Write-Host "  用户ID: $($login2.data.id)" -ForegroundColor Cyan
    Write-Host "  VIP类型: $($login2.data.vip.product)" -ForegroundColor Cyan
    Write-Host "  权限等级: $($login2.data.vip.power)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ Basic用户登录失败" -ForegroundColor Red
    exit 1
}

# 测试4: JWT认证
Write-Host "`n4. JWT认证测试..." -ForegroundColor Yellow
try {
    $headers = @{ 'X-Auth-Token' = $token1; 'Content-Type' = 'application/json' }
    $whoami = Invoke-RestMethod -Uri "http://localhost:8081/users/whoami" -Method POST -Headers $headers -Body "{}"
    
    Write-Host "✓ JWT认证成功" -ForegroundColor Green
    Write-Host "  激活码: $($whoami.data.activationCode)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ JWT认证失败" -ForegroundColor Red
    exit 1
}

# 测试总结
Write-Host "`n=== 测试结果 ===" -ForegroundColor Magenta
Write-Host "✓ 所有功能测试通过!" -ForegroundColor Green
Write-Host "✓ 激活码登录正常工作" -ForegroundColor Green
Write-Host "✓ JWT认证正常工作" -ForegroundColor Green
Write-Host "✓ 安全配置正确" -ForegroundColor Green
Write-Host "`n应用已准备就绪!" -ForegroundColor Green

Write-Host "`n可用的测试激活码:" -ForegroundColor Yellow
Write-Host "1. test123456789 (premium用户)" -ForegroundColor Cyan
Write-Host "2. demo987654321 (basic用户)" -ForegroundColor Cyan
