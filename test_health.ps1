# 测试健康检查接口
Write-Host "测试健康检查接口..." -ForegroundColor Yellow

try {
    $response = Invoke-RestMethod -Uri "http://localhost:8081/health" -Method GET
    
    Write-Host "健康检查成功!" -ForegroundColor Green
    Write-Host "状态: $($response.data.status)" -ForegroundColor Green
    Write-Host "应用: $($response.data.application)" -ForegroundColor Green
    Write-Host "版本: $($response.data.version)" -ForegroundColor Green
    Write-Host "消息: $($response.data.message)" -ForegroundColor Green
    
} catch {
    Write-Host "健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
}
