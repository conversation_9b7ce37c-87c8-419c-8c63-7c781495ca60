# Test frontend functionality

Write-Host "=== Frontend Functionality Test ===" -ForegroundColor Green

# Test if frontend is accessible
Write-Host "`n1. Testing frontend accessibility..." -ForegroundColor Yellow
try {
    $frontendResponse = Invoke-WebRequest -Uri 'http://localhost:3001' -Method GET
    Write-Host "Frontend accessible: $($frontendResponse.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "Frontend not accessible: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test API proxy through frontend
Write-Host "`n2. Testing API proxy through frontend..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-WebRequest -Uri 'http://localhost:3001/api/health' -Method GET
    Write-Host "API proxy working: $($healthResponse.StatusCode)" -ForegroundColor Green
    $healthData = $healthResponse.Content | ConvertFrom-Json
    Write-Host "Backend status through proxy: $($healthData.data.status)" -ForegroundColor Green
} catch {
    Write-Host "API proxy failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test admin login through frontend proxy
Write-Host "`n3. Testing admin login through frontend proxy..." -ForegroundColor Yellow
try {
    $loginBody = @{
        username = "root"
        password = "admin123"
    } | ConvertTo-Json
    
    $loginResponse = Invoke-WebRequest -Uri 'http://localhost:3001/api/admin/login' -Method POST -Body $loginBody -ContentType 'application/json'
    Write-Host "Admin login through proxy: $($loginResponse.StatusCode)" -ForegroundColor Green
    $loginData = $loginResponse.Content | ConvertFrom-Json
    $token = $loginData.data.token
    Write-Host "Token received through proxy: $($token.Substring(0, 20))..." -ForegroundColor Green
    
    # Test activation code generation through frontend proxy
    Write-Host "`n4. Testing activation code generation through frontend proxy..." -ForegroundColor Yellow
    $generateBody = @{
        expireDays = 365
        agent = "main"
        product = "premium"
        power = 5
        score = 1000
        dayScore = 100.0
    } | ConvertTo-Json
    
    $headers = @{
        'X-Auth-Token' = $token
        'Content-Type' = 'application/json'
    }
    
    $generateResponse = Invoke-WebRequest -Uri 'http://localhost:3001/api/users/generate-activation-code' -Method POST -Body $generateBody -Headers $headers
    Write-Host "Activation code generation through proxy: $($generateResponse.StatusCode)" -ForegroundColor Green
    $generateData = $generateResponse.Content | ConvertFrom-Json
    $activationCode = $generateData.data.activationCode
    Write-Host "Generated activation code through proxy: $activationCode" -ForegroundColor Green
    
    # Test activation code login through frontend proxy
    Write-Host "`n5. Testing activation code login through frontend proxy..." -ForegroundColor Yellow
    $cardLoginBody = @{
        card = $activationCode
        agent = "main"
    } | ConvertTo-Json
    
    $cardLoginResponse = Invoke-WebRequest -Uri 'http://localhost:3001/api/users/card-login' -Method POST -Body $cardLoginBody -ContentType 'application/json'
    Write-Host "Activation code login through proxy: $($cardLoginResponse.StatusCode)" -ForegroundColor Green
    $cardLoginData = $cardLoginResponse.Content | ConvertFrom-Json
    Write-Host "Login successful for user: $($cardLoginData.data.userId)" -ForegroundColor Green
    
} catch {
    Write-Host "Admin login through proxy failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $errorContent = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorContent)
        $errorText = $reader.ReadToEnd()
        Write-Host "Error details: $errorText" -ForegroundColor Red
    }
}

Write-Host "`n=== Frontend Test Completed ===" -ForegroundColor Green
Write-Host "`nFrontend is running at: http://localhost:3001" -ForegroundColor Cyan
Write-Host "Backend is running at: http://localhost:8082" -ForegroundColor Cyan
Write-Host "`nYou can now:" -ForegroundColor Yellow
Write-Host "1. Open http://localhost:3001 in your browser" -ForegroundColor White
Write-Host "2. Navigate to the Activation Code Generator page" -ForegroundColor White
Write-Host "3. Generate activation codes and test the login functionality" -ForegroundColor White
Write-Host "4. Use the User Test page to test various scenarios" -ForegroundColor White
